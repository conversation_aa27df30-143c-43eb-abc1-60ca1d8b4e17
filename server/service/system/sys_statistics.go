package system

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"

	// "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

	systemRes "github.com/flipped-aurora/gin-vue-admin/server/model/system/response"
	// "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	// "github.com/gin-gonic/gin"
	"fmt"
	"math"
	"time"

	"errors"

	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
)

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetSystemConfig
//@description: 读取配置文件
//@return: conf config.Server, err error

type StatisticsService struct{}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetWechatVoucherSettingList
//@description: 图表接口,
//@param: api model.SysApi, info request.PageInfo, order string, desc bool
//@return: list interface{}, total int64, err error

func (statisticsService *StatisticsService) GetAllOverviewInfo(searchStatisticsParams systemReq.SearchStatisticsParams, userId uint, authorityId uint) (res systemRes.SysStatisticsResponse, err error) {

	if searchStatisticsParams.Type == "1" {
		res, err = statisticsService.WechatBankEstablishedDepositReduction(searchStatisticsParams, userId, authorityId)
	} else if searchStatisticsParams.Type == "2" {
		res, err = statisticsService.AlipayGeneralRedEnvelopeInfo(searchStatisticsParams, userId, authorityId)
	} else if searchStatisticsParams.Type == "3" {
		res, err = statisticsService.WechatRedBank(searchStatisticsParams, userId, authorityId)
	}
	return res, err
}

// 支付宝通用红包
func (statisticsService *StatisticsService) AlipayGeneralRedEnvelopeInfo(searchStatisticsParams systemReq.SearchStatisticsParams, userId uint, authorityId uint) (res systemRes.SysStatisticsResponse, err error) {

	var categorizeResults systemRes.CategorizeResults
	// 查询公司appid和总余额
	// 客户权限设置
	var sysCompaniesApi system.SysCompaniesApi

	var alipayStockId []string

	// db3 :=
	db3 := global.GVA_DB.Model(&system.SysAlipayVoucherSettingGroupApi{})

	// 查询批量制券总数
	var batchTaskTotal int64
	var batchTaskAlipayAmountTotal float64
	var batchTaskAlipayReceiveTotal int64
	var batchTaskAlipayAmountReceiveTotal float64
	db4 := global.GVA_DB.Model(&system.SysMultipleTaskApi{})

	db5 := global.GVA_DB.Model(&system.SysMultipleTaskResApi{})

	fmt.Println(alipayStockId)
	// 查询支付宝制券总数
	var alipayTotal int64
	// 查询支付宝领取总数
	var alipayReceiveTotal int64
	db1 := global.GVA_DB.Model(&system.SysAlipayVoucherApi{})
	// 查询支付宝核销总数
	var alipayWriteOffTotal int64
	db2 := global.GVA_DB.Model(&system.SysVoucharAlipayAallbackListApi{})
	// 查询制券总金额
	var alipayAmountTotal float64
	// 查询领取总金额
	var alipayAmountReceiveTotal float64
	// 查询制核销金额
	var alipayAmountWriteOffTotal float64

	for _, date := range utils.Last7Days() {
		categorizeResults.CategoryDate = append(categorizeResults.CategoryDate, date.Format("2006-01-02"))
	}
	// 翻转日期
	categorizeResults.CategoryDate = utils.ReverseSlice(categorizeResults.CategoryDate)
	categorizeResults.SubjectNameList = []string{"制券金额", "领取金额", "核销金额"}
	categorizeResults.CategorizeName = "统计"
	// 存储客户的 AppIDs
	var customerAppIds []string
	// 客户权限设置
	if authorityId == 555 {
		// 查询用户的appid
		result := global.GVA_DB.Where("user_id = ? ", userId).First(&sysCompaniesApi)
		if result.Error != nil {
			res.Charset = categorizeResults
			// 记录不存在或查询错误
			return res, err
		}
		customerAppIds = []string{sysCompaniesApi.Appid}
		// db1.Where("appid = ? ", sysCompaniesApi.Appid)
		// db2.Where("appid = ? ", sysCompaniesApi.Appid)
		// db3.Where("appid = ? ",sysCompaniesApi.Appid)
		// 总金额
		res.Value1 = sysCompaniesApi.TotalAmount
	} else if authorityId == 666 { // 渠道用户，查看关联的客户数据
		// 获取渠道ID
		channelId := utils.GetChannelId(userId)
		if channelId == 0 {
			return res, errors.New("获取渠道信息失败")
		}

		// 获取渠道下所有客户的AppID
		err := global.GVA_DB.Raw(`
			SELECT c.appid 
			FROM channel_user_middle m 
			JOIN companies c ON m.companies_id = c.id 
			WHERE m.channel_user_id = ?
		`, userId).Pluck("appid", &customerAppIds).Error
		if err != nil {
			return res, fmt.Errorf("获取客户AppID失败: %v", err)
		}

		if len(customerAppIds) == 0 {
			return res, nil // 没有关联的客户，返回空结果
		}
	}
	if authorityId != 888 {
		if len(customerAppIds) == 0 {
			return res, nil // 直接返回空结果
		}
		db1.Where("appid IN ?", customerAppIds)
		db3.Where("appid IN ?", customerAppIds).Pluck("alipay_stock_id", &alipayStockId)
		// ----
		db2.Where("activity_id  IN ?", alipayStockId)

		db4.Where("appid IN ?", customerAppIds)
		db5.Where("appid IN ?", customerAppIds)
		// ----
		// db3.Where("appid = ? ",sysCompaniesApi.Appid)
	} else {
		db3.Pluck("alipay_stock_id", &alipayStockId)
		// ----
		db2.Where("activity_id  IN ?", alipayStockId)

		// db4.Where("appid IN ?", customerAppIds)
	}
	if searchStatisticsParams.AppId != "" {
		result := global.GVA_DB.Where("appid = ? ", searchStatisticsParams.AppId).First(&sysCompaniesApi)
		if result.Error != nil {
			res.Charset = categorizeResults
			// 记录不存在或查询错误
			return res, err
		}
		db1.Where("appid = ? ", sysCompaniesApi.Appid)

		db3.Where("appid = ?", searchStatisticsParams.AppId).Pluck("alipay_stock_id", &alipayStockId)
		// ----
		db2.Where("activity_id  IN ?", alipayStockId)
		db4.Where("appid = ? ", sysCompaniesApi.Appid)
		db5.Where("appid = ? ", sysCompaniesApi.Appid)
		// ----
		// db3.Where("appid = ? ",sysCompaniesApi.Appid)
		// 总金额
		res.Value1 = sysCompaniesApi.TotalAmount
	}
	// 如果有条件搜索 下方会自动创建搜索语句
	if searchStatisticsParams.StartCreatedAt != nil && searchStatisticsParams.EndCreatedAt != nil {
		db1.Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Count(&alipayTotal)
		// db3.Count(&alipayTotal)
		db1.Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Select("COALESCE(SUM(amount), 0)").Scan(&alipayAmountTotal)

		db1.Where("collection_time BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Where("status >= ? ", 1).Count(&alipayReceiveTotal)
		db1.Where("collection_time BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Where("status >= ? ", 1).Select("COALESCE(SUM(amount), 0)").Scan(&alipayAmountReceiveTotal)

		db2.Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Where("biz_type = ? ", "V_USE").Count(&alipayWriteOffTotal)
		db2.Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Where("biz_type = ? ", "V_USE").Select("COALESCE(SUM(publish_amount), 0)").Scan(&alipayAmountWriteOffTotal)
		// 批量
		db4.Where("start_time BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Where("status = ? ", 2).Select("COALESCE(SUM(task_num), 0)").Scan(&batchTaskTotal)
		db4.Where("start_time BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Where("status = ? ", 2).Select("COALESCE(SUM(task_amount), 0)").Scan(&batchTaskAlipayAmountTotal)

		db5.Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Where("send_status = ? ", "SUCCESS").Count(&batchTaskAlipayReceiveTotal)
		db5.Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Where("send_status = ? ", "SUCCESS").Select("COALESCE(SUM(send_amount), 0)").Scan(&batchTaskAlipayAmountReceiveTotal)

		localTimeStartCreatedAt := searchStatisticsParams.StartCreatedAt.In(time.Local)
		localTimeEndCreatedAt := searchStatisticsParams.EndCreatedAt.In(time.Local)
		dateList, err := utils.GenerateDates(
			localTimeStartCreatedAt.Format("2006-01-02"),
			localTimeEndCreatedAt.Format("2006-01-02"),
		)
		if err != nil {
			return res, fmt.Errorf("日期生成失败: %v", err)
		}

		// 确保日期格式统一（YYYY-MM-DD）
		formattedDates := make([]string, len(dateList))
		for i, dateStr := range dateList {
			t, err := time.Parse("2006-1-2", dateStr) // 解析原始格式
			if err != nil {
				return res, fmt.Errorf("日期解析错误: %v", err)
			}
			formattedDates[i] = t.Format("2006-01-02") // 统一格式
		}

		categorizeResults.CategoryDate = formattedDates
	} else {
		db1.Count(&alipayTotal)
		// db3.Count(&alipayTotal)
		db1.Select("COALESCE(SUM(amount), 0)").Scan(&alipayAmountTotal)

		db1.Where("status >= ? ", 1).Count(&alipayReceiveTotal)
		db1.Where("status >= ? ", 1).Select("COALESCE(SUM(amount), 0)").Scan(&alipayAmountReceiveTotal)

		db2.Where("biz_type = ? ", "V_USE").Count(&alipayWriteOffTotal)
		db2.Where("biz_type = ? ", "V_USE").Select("COALESCE(SUM(publish_amount), 0)").Scan(&alipayAmountWriteOffTotal)
		// 批量
		db4.Where("status = ? ", 2).Select("COALESCE(SUM(task_num), 0)").Scan(&batchTaskTotal)
		db4.Where("status = ? ", 2).Select("COALESCE(SUM(task_amount), 0)").Scan(&batchTaskAlipayAmountTotal)
		db5.Where("send_status = ? ", "SUCCESS").Count(&batchTaskAlipayReceiveTotal)
		db5.Where("send_status = ? ", "SUCCESS").Select("COALESCE(SUM(send_amount), 0)").Scan(&batchTaskAlipayAmountReceiveTotal)
	}
	// 总制券数包含支付宝和批量
	res.Value2 = alipayTotal + batchTaskTotal
	// 总领取数包含支付宝和批量
	res.Value3 = alipayReceiveTotal + batchTaskAlipayReceiveTotal
	// // 核销总数包含微信支付宝和批量
	// res.Value4 = alipayWriteOffTotal
	res.Value5 = alipayAmountTotal + batchTaskAlipayAmountTotal
	res.Value6 = alipayAmountReceiveTotal + batchTaskAlipayAmountReceiveTotal
	res.Value4 = alipayWriteOffTotal
	res.Value7 = alipayAmountWriteOffTotal

	//制券金额
	type CountListResults1 struct {
		FormattedDate string  `json:"formattedDate" gorm:"column:formattedDate"`
		Amount        float64 `json:"amount" gorm:"column:amount"`
	}
	var countListResults1 []CountListResults1
	err = db1.Debug().Select("DATE_FORMAT(created_at, '%Y-%m-%d') as formattedDate", "COALESCE(SUM(amount), 0) as amount").Where("created_at>= ? and created_at<= ?", categorizeResults.CategoryDate[0]+" 00:00:00", categorizeResults.CategoryDate[len(categorizeResults.CategoryDate)-1]+" 23:59:59").Group("formattedDate").Find(&countListResults1).Error

	//领取金额
	type CountListResults2 struct {
		FormattedDate string  `json:"formattedDate" gorm:"column:formattedDate"`
		Amount        float64 `json:"account" gorm:"column:amount"`
	}
	var countListResults2 []CountListResults2
	err = db1.Debug().Select("DATE_FORMAT(created_at, '%Y-%m-%d') as formattedDate", "COALESCE(SUM(amount), 0) as amount").Where("created_at>= ? and created_at<= ?", categorizeResults.CategoryDate[0]+" 00:00:00", categorizeResults.CategoryDate[len(categorizeResults.CategoryDate)-1]+" 23:59:59").Group("formattedDate").Find(&countListResults2).Error
	//核销金额
	type CountListResults3 struct {
		FormattedDate string  `json:"formattedDate" gorm:"column:formattedDate"`
		Amount        float64 `json:"amount" gorm:"column:amount"`
	}
	var countListResults3 []CountListResults3
	err = db2.Debug().Select("DATE_FORMAT(created_at, '%Y-%m-%d') as formattedDate", "COALESCE(SUM(publish_amount), 0) as amount").Where("created_at>= ? and created_at<= ?", categorizeResults.CategoryDate[0]+" 00:00:00", categorizeResults.CategoryDate[len(categorizeResults.CategoryDate)-1]+" 23:59:59").Group("formattedDate").Find(&countListResults3).Error

	// 1. 定义统一数据结构
	type ChartSeries struct {
		Name string    `json:"name"`
		Type string    `json:"type"`
		Data []float64 `json:"data"`
	}

	// 2. 创建日期索引映射
	dateIndex := make(map[string]int)
	for i, date := range categorizeResults.CategoryDate {
		dateIndex[date] = i
	}

	// 3. 初始化结果集
	var dataRes []ChartSeries

	// 4. 处理制券金额
	createSeries := ChartSeries{
		Name: "制券金额",
		Type: "line",
		Data: make([]float64, len(categorizeResults.CategoryDate)), // 使用 float64 初始化,
	}
	fmt.Println(categorizeResults.CategoryDate)
	for _, item := range countListResults1 {
		if idx, ok := dateIndex[item.FormattedDate]; ok {
			createSeries.Data[idx] = item.Amount
		}
	}
	dataRes = append(dataRes, createSeries)

	// 5. 处理领取金额
	receiveSeries := ChartSeries{
		Name: "领取金额",
		Type: "line",
		Data: make([]float64, len(categorizeResults.CategoryDate)), // 使用 float64 初始化,
	}
	for _, item := range countListResults2 {
		if idx, ok := dateIndex[item.FormattedDate]; ok {
			receiveSeries.Data[idx] = item.Amount
		}
	}
	dataRes = append(dataRes, receiveSeries)

	// 6. 处理核销金额
	useSeries := ChartSeries{
		Name: "核销金额",
		Type: "line",
		Data: make([]float64, len(categorizeResults.CategoryDate)), // 使用 float64 初始化,
	}
	for _, item := range countListResults3 {
		if idx, ok := dateIndex[item.FormattedDate]; ok {
			useSeries.Data[idx] = item.Amount
		}
	}
	dataRes = append(dataRes, useSeries)

	// 7. 赋值最终结果
	categorizeResults.CategoryCountList = dataRes

	res.Charset = categorizeResults

	return res, err
}

// 微信银行立减金
func (statisticsService *StatisticsService) WechatBankEstablishedDepositReduction(searchStatisticsParams systemReq.SearchStatisticsParams, userId uint, authorityId uint) (res systemRes.SysStatisticsResponse, err error) {

	var categorizeResults systemRes.CategorizeResults
	// 查询公司appid和总余额
	// 客户权限设置
	var sysCompaniesApi system.SysCompaniesApi

	// 查询微信制券总数
	var wechatTotal int64
	// 查询微信领取总数
	var wechatReceiveTotal int64
	db1 := global.GVA_DB.Model(&system.SysWechatVoucherApi{})
	// 查询微信核销总数
	var wechatWriteOffTotal int64
	db2 := global.GVA_DB.Model(&system.SysWechatVoucharCallbackListApi{})
	// 查询制券总金额
	var wechatAmountTotal float64
	// 查询领取总金额
	var wechatAmountReceiveTotal float64
	// 查询制核销金额
	var wechatAmountWriteOffTotal float64
	for _, date := range utils.Last7Days() {
		categorizeResults.CategoryDate = append(categorizeResults.CategoryDate, date.Format("2006-01-02"))
	}
	// 翻转日期
	categorizeResults.CategoryDate = utils.ReverseSlice(categorizeResults.CategoryDate)
	categorizeResults.SubjectNameList = []string{"制券金额", "领取金额", "核销金额"}
	categorizeResults.CategorizeName = "统计"
	// 存储客户的 AppIDs
	var customerAppIds []string
	// 客户权限设置
	if authorityId == 555 {
		// 查询用户的appid
		result := global.GVA_DB.Where("user_id = ? ", userId).First(&sysCompaniesApi)
		if result.Error != nil {
			res.Charset = categorizeResults
			// 记录不存在或查询错误
			return res, err
		}
		customerAppIds = []string{sysCompaniesApi.Appid}
		// db1.Where("appid = ? ", sysCompaniesApi.Appid)
		// db2.Where("appid = ? ", sysCompaniesApi.Appid)
		// db3.Where("appid = ? ",sysCompaniesApi.Appid)
		// 总金额
		res.Value1 = sysCompaniesApi.TotalAmount
	} else if authorityId == 666 { // 渠道用户，查看关联的客户数据
		// 获取渠道ID
		channelId := utils.GetChannelId(userId)
		if channelId == 0 {
			return res, errors.New("获取渠道信息失败")
		}

		// 获取渠道下所有客户的AppID
		err := global.GVA_DB.Raw(`
			SELECT c.appid 
			FROM channel_user_middle m 
			JOIN companies c ON m.companies_id = c.id 
			WHERE m.channel_user_id = ?
		`, userId).Pluck("appid", &customerAppIds).Error
		if err != nil {
			return res, fmt.Errorf("获取客户AppID失败: %v", err)
		}

		if len(customerAppIds) == 0 {
			return res, nil // 没有关联的客户，返回空结果
		}
	}
	if authorityId != 888 {
		if len(customerAppIds) == 0 {
			return res, nil // 直接返回空结果
		}
		db1.Where("appid IN ?", customerAppIds)
		db2.Where("appid IN ?", customerAppIds)
		// db3.Where("appid = ? ",sysCompaniesApi.Appid)
	}
	if searchStatisticsParams.AppId != "" {
		result := global.GVA_DB.Where("appid = ? ", searchStatisticsParams.AppId).First(&sysCompaniesApi)
		if result.Error != nil {
			res.Charset = categorizeResults
			// 记录不存在或查询错误
			return res, err
		}
		db1.Where("appid = ? ", sysCompaniesApi.Appid)
		db2.Where("appid = ? ", sysCompaniesApi.Appid)
		// db3.Where("appid = ? ",sysCompaniesApi.Appid)
		// 总金额
		res.Value1 = sysCompaniesApi.TotalAmount
	}
	// 如果有条件搜索 下方会自动创建搜索语句
	if searchStatisticsParams.StartCreatedAt != nil && searchStatisticsParams.EndCreatedAt != nil {
		db1.Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Count(&wechatTotal)
		// db3.Count(&alipayTotal)
		db1.Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Select("COALESCE(SUM(denomination), 0)").Scan(&wechatAmountTotal)

		db1.Where("collection_time BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Where("status = ? ", 1).Count(&wechatReceiveTotal)
		db1.Where("collection_time BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Where("status = ? ", 1).Select("COALESCE(SUM(denomination), 0)").Scan(&wechatAmountReceiveTotal)

		db2.Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Where("status = ? ", "USED").Count(&wechatWriteOffTotal)
		db2.Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Where("status = ? ", "USED").Select("COALESCE(SUM(coupon_amount), 0)").Scan(&wechatAmountWriteOffTotal)
		localTimeStartCreatedAt := searchStatisticsParams.StartCreatedAt.In(time.Local)
		localTimeEndCreatedAt := searchStatisticsParams.EndCreatedAt.In(time.Local)
		dateList, err := utils.GenerateDates(
			localTimeStartCreatedAt.Format("2006-01-02"),
			localTimeEndCreatedAt.Format("2006-01-02"),
		)
		if err != nil {
			return res, fmt.Errorf("日期生成失败: %v", err)
		}

		// 确保日期格式统一（YYYY-MM-DD）
		formattedDates := make([]string, len(dateList))
		for i, dateStr := range dateList {
			t, err := time.Parse("2006-1-2", dateStr) // 解析原始格式
			if err != nil {
				return res, fmt.Errorf("日期解析错误: %v", err)
			}
			formattedDates[i] = t.Format("2006-01-02") // 统一格式
		}

		categorizeResults.CategoryDate = formattedDates
	} else {
		db1.Count(&wechatTotal)
		// db3.Count(&alipayTotal)
		db1.Select("COALESCE(SUM(denomination), 0)").Scan(&wechatAmountTotal)

		db1.Where("status = ? ", 1).Count(&wechatReceiveTotal)
		db1.Select("COALESCE(SUM(denomination), 0)").Scan(&wechatAmountReceiveTotal)

		db2.Where("status = ? ", "USED").Count(&wechatWriteOffTotal)
		db2.Select("COALESCE(SUM(coupon_amount), 0)").Scan(&wechatAmountWriteOffTotal)

	}

	// 总制券数包含微信
	res.Value2 = wechatTotal
	// 总领取数包含微
	res.Value3 = wechatReceiveTotal
	// 核销总数包含微信
	res.Value4 = wechatWriteOffTotal

	res.Value5 = wechatAmountTotal
	res.Value6 = wechatAmountReceiveTotal
	res.Value7 = wechatAmountWriteOffTotal
	//制券金额
	type CountListResults1 struct {
		FormattedDate string `json:"formattedDate" gorm:"column:formattedDate"`
		Amount        int    `json:"amount" gorm:"column:amount"`
	}
	var countListResults1 []CountListResults1
	err = db1.Debug().Select("DATE_FORMAT(created_at, '%Y-%m-%d') as formattedDate", "COALESCE(SUM(denomination), 0) as amount").Where("created_at>= ? and created_at<= ?", categorizeResults.CategoryDate[0]+" 00:00:00", categorizeResults.CategoryDate[len(categorizeResults.CategoryDate)-1]+" 23:59:59").Group("formattedDate").Find(&countListResults1).Error

	fmt.Println("--------------------------------")
	fmt.Println(countListResults1)
	fmt.Println("--------------------------------")

	//领取金额
	type CountListResults2 struct {
		FormattedDate string `json:"formattedDate" gorm:"column:formattedDate"`
		Amount        int    `json:"account" gorm:"column:amount"`
	}
	var countListResults2 []CountListResults2
	err = db1.Debug().Select("DATE_FORMAT(created_at, '%Y-%m-%d') as formattedDate", "COALESCE(SUM(denomination), 0) as amount").Where("created_at>= ? and created_at<= ? AND status = ?", categorizeResults.CategoryDate[0]+" 00:00:00", categorizeResults.CategoryDate[len(categorizeResults.CategoryDate)-1]+" 23:59:59", 1).Group("formattedDate").Find(&countListResults2).Error
	//核销金额
	type CountListResults3 struct {
		FormattedDate string `json:"formattedDate" gorm:"column:formattedDate"`
		Amount        int    `json:"amount" gorm:"column:amount"`
	}
	var countListResults3 []CountListResults3
	err = db2.Debug().Select("DATE_FORMAT(created_at, '%Y-%m-%d') as formattedDate", "COALESCE(SUM(coupon_amount), 0) as amount").Where("created_at>= ? and created_at<= ? AND status = ?", categorizeResults.CategoryDate[0]+" 00:00:00", categorizeResults.CategoryDate[len(categorizeResults.CategoryDate)-1]+" 23:59:59", "USED").Group("formattedDate").Find(&countListResults3).Error

	// 1. 定义统一数据结构
	type ChartSeries struct {
		Name string `json:"name"`
		Type string `json:"type"`
		Data []int  `json:"data"`
	}

	// 2. 创建日期索引映射
	dateIndex := make(map[string]int)
	for i, date := range categorizeResults.CategoryDate {
		dateIndex[date] = i
	}

	// 3. 初始化结果集
	var dataRes []ChartSeries

	// 4. 处理制券金额
	createSeries := ChartSeries{
		Name: "制券金额",
		Type: "line",
		Data: make([]int, len(categorizeResults.CategoryDate)),
	}
	fmt.Println(categorizeResults.CategoryDate)
	for _, item := range countListResults1 {
		if idx, ok := dateIndex[item.FormattedDate]; ok {
			createSeries.Data[idx] = item.Amount
		}
	}
	dataRes = append(dataRes, createSeries)

	// 5. 处理领取金额
	receiveSeries := ChartSeries{
		Name: "领取金额",
		Type: "line",
		Data: make([]int, len(categorizeResults.CategoryDate)),
	}
	for _, item := range countListResults2 {
		if idx, ok := dateIndex[item.FormattedDate]; ok {
			receiveSeries.Data[idx] = item.Amount
		}
	}
	dataRes = append(dataRes, receiveSeries)

	// 6. 处理核销金额
	useSeries := ChartSeries{
		Name: "核销金额",
		Type: "line",
		Data: make([]int, len(categorizeResults.CategoryDate)),
	}
	for _, item := range countListResults3 {
		if idx, ok := dateIndex[item.FormattedDate]; ok {
			useSeries.Data[idx] = item.Amount
		}
	}
	dataRes = append(dataRes, useSeries)

	// 7. 赋值最终结果
	categorizeResults.CategoryCountList = dataRes

	res.Charset = categorizeResults

	return res, err

}

// GetChannelOverviewInfo 获取渠道概览信息
func (statisticsService *StatisticsService) GetChannelOverviewInfo(userId uint, authorityId uint) (systemRes.ChannelOverviewResponse, error) {
	db := global.GVA_DB
	var response systemRes.ChannelOverviewResponse

	// 获取渠道信息
	var channel system.SysChannel
	if err := db.Where("user_id = ?", userId).First(&channel).Error; err != nil {
		return response, errors.New("未找到渠道信息")
	}

	// 设置渠道账户余额
	response.ChannelAmount = channel.TotalAmount

	// 获取渠道下的客户总数
	var customerCount int64
	err := db.Model(&system.SysChannelUserMiddle{}).
		Where("channel_user_id = ?", userId).
		Count(&customerCount).Error
	if err != nil {
		return response, err
	}
	response.CustomerCount = customerCount

	// ====== 开始计算核销金额和佣金 =======

	// 获取渠道下所有客户的AppID
	var customerAppIds []string
	err = db.Raw(`
		SELECT c.appid 
		FROM channel_user_middle m 
		JOIN companies c ON m.companies_id = c.id 
		WHERE m.channel_user_id = ?
	`, userId).Pluck("appid", &customerAppIds).Error
	if err != nil {
		return response, fmt.Errorf("获取客户AppID失败: %v", err)
	}

	if len(customerAppIds) == 0 {
		// 没有客户，返回默认的空结果
		return response, nil
	}

	// 查询所有客户的公司名称和ID
	var companyIds = make(map[string]uint)
	type CompanyInfo struct {
		ID    uint   `gorm:"column:id"`
		Appid string `gorm:"column:appid"`
	}
	var companyInfos []CompanyInfo
	db.Table("companies").Where("appid IN ?", customerAppIds).
		Select("id, appid").
		Find(&companyInfos)

	for _, info := range companyInfos {
		companyIds[info.Appid] = info.ID
	}

	// 获取税地信息
	type TaxLocationInfo struct {
		ID   uint    `gorm:"column:id"`
		Rate float64 `gorm:"column:rate"`
	}
	var taxLocations []TaxLocationInfo
	db.Table("tax_location_list").Select("id, rate").Find(&taxLocations)

	// 获取渠道ID
	channelId := utils.GetChannelId(userId)

	// 获取平台给渠道的税率
	type PlatformChannelRate struct {
		TaxLocationID  uint    `gorm:"column:tax_territory_id"`
		TaxTerritoryID uint    `gorm:"column:id"`
		PlatformRate   float64 `gorm:"column:platform_rate"`
	}
	var platformChannelRates []PlatformChannelRate
	db.Table("tax_territory_list").
		Select("id, tax_territory_id, platform_rate").
		Where("scene_id = ? AND customer_type = 1 AND status = 1", channelId).
		Find(&platformChannelRates)

	// 构建平台给渠道的税率映射 (税地ID -> [税地列表ID, 平台费率])
	platformRateMap := make(map[uint]map[string]interface{})
	for _, rate := range platformChannelRates {
		platformRateMap[rate.TaxLocationID] = map[string]interface{}{
			"tax_territory_list_id": rate.TaxTerritoryID,
			"platform_rate":         rate.PlatformRate,
		}
	}

	// 获取渠道给客户的税率
	type ChannelCustomerRate struct {
		CompanyID          uint    `gorm:"column:companies_id"`
		TaxTerritoryListID uint    `gorm:"column:tax_territory_list_id"`
		TaxLocationID      uint    `gorm:"column:tax_territory_id"`
		CustomerRate       float64 `gorm:"column:customer_rate"`
	}
	var channelCustomerRates []ChannelCustomerRate
	db.Table("channel_tax_territory_list").
		Select("companies_id, tax_territory_list_id, tax_territory_id, customer_rate").
		Where("status = 1").
		Find(&channelCustomerRates)

	// 构建渠道给客户的税率映射 (公司ID -> 税地ID -> 客户费率)
	customerRateMap := make(map[uint]map[uint]float64)
	for _, rate := range channelCustomerRates {
		if _, exists := customerRateMap[rate.CompanyID]; !exists {
			customerRateMap[rate.CompanyID] = make(map[uint]float64)
		}
		customerRateMap[rate.CompanyID][rate.TaxLocationID] = rate.CustomerRate
	}

	// 获取微信和支付宝活动ID及其关联的税地ID
	// 微信活动和税地映射
	wechatStockTaxMap := make(map[string]uint) // stock_id -> 税地ID 映射
	wechatStockMap := make(map[string]string)  // stock_id -> appid 映射

	{
		type WechatVoucherActivity struct {
			ID            string `gorm:"column:id"`
			Appid         string `gorm:"column:appid"`
			TaxLocationID uint   `gorm:"column:tax_location_id"`
			WechatStockID string `gorm:"column:wechat_stock_id"`
		}
		var wechatVoucherActivities []WechatVoucherActivity

		db.Table("wechat_voucher_setting_group").
			Select("id, appid, tax_location_id, wechat_stock_id").
			Where("appid IN ?", customerAppIds).
			Find(&wechatVoucherActivities)

		for _, activity := range wechatVoucherActivities {
			if activity.WechatStockID != "" {
				wechatStockMap[activity.WechatStockID] = activity.Appid
				wechatStockTaxMap[activity.WechatStockID] = activity.TaxLocationID
			}
		}
	}

	// 支付宝活动和税地映射
	alipayStockTaxMap := make(map[string]uint) // activity_id -> 税地ID 映射
	alipayStockMap := make(map[string]string)  // activity_id -> appid 映射

	{
		type AlipayVoucherActivity struct {
			ID            string `gorm:"column:id"`
			Appid         string `gorm:"column:appid"`
			TaxLocationID uint   `gorm:"column:tax_location_id"`
			AlipayStockID string `gorm:"column:alipay_stock_id"`
		}
		var alipayVoucherActivities []AlipayVoucherActivity

		db.Table("voucher_setting_group").
			Select("id, appid, tax_location_id, alipay_stock_id").
			Where("appid IN ?", customerAppIds).
			Find(&alipayVoucherActivities)

		for _, activity := range alipayVoucherActivities {
			if activity.AlipayStockID != "" {
				alipayStockMap[activity.AlipayStockID] = activity.Appid
				alipayStockTaxMap[activity.AlipayStockID] = activity.TaxLocationID
			}
		}
	}

	// 查询结果容器
	type DetailResult struct {
		Appid        string  `json:"appid"`
		Platform     int     `json:"platform"`
		CouponAmount float64 `json:"coupon_amount"`
		ActivityID   string  `json:"activity_id"`
	}
	var detailResults []DetailResult

	// 查询微信核销记录
	{
		var wechatResults []DetailResult
		wechatStockIDs := make([]string, 0, len(wechatStockMap))
		for stockID := range wechatStockMap {
			wechatStockIDs = append(wechatStockIDs, stockID)
		}

		if len(wechatStockIDs) > 0 {
			wechatQuery := db.Table("wechat_vouchar_callback_list").
				Select("stock_id as activity_id, coupon_amount").
				Where("stock_id IN ?", wechatStockIDs)

			var wechatTempResults []struct {
				ActivityID   string  `gorm:"column:activity_id"`
				CouponAmount float64 `gorm:"column:coupon_amount"`
			}
			wechatQuery.Find(&wechatTempResults)

			// 设置平台类型为微信，并通过stock_id找回对应的appid
			for _, result := range wechatTempResults {
				if appid, exists := wechatStockMap[result.ActivityID]; exists {
					wechatResults = append(wechatResults, DetailResult{
						Appid:        appid,
						Platform:     1, // 微信
						CouponAmount: result.CouponAmount,
						ActivityID:   result.ActivityID,
					})
				}
			}
			detailResults = append(detailResults, wechatResults...)
		}
	}

	// 查询支付宝核销记录
	{
		var alipayResults []DetailResult
		alipayStockIDs := make([]string, 0, len(alipayStockMap))
		for stockID := range alipayStockMap {
			alipayStockIDs = append(alipayStockIDs, stockID)
		}

		if len(alipayStockIDs) > 0 {
			alipayQuery := db.Table("vouchar_alipay_callback_list").
				Select("activity_id, publish_amount as coupon_amount").
				Where("activity_id IN ?", alipayStockIDs).
				Where("biz_type = 'V_USE'") // 只查询核销记录

			var alipayTempResults []struct {
				ActivityID   string  `gorm:"column:activity_id"`
				CouponAmount float64 `gorm:"column:coupon_amount"`
			}
			alipayQuery.Find(&alipayTempResults)

			// 设置平台类型为支付宝，并通过activity_id找回对应的appid
			for _, result := range alipayTempResults {
				if appid, exists := alipayStockMap[result.ActivityID]; exists {
					alipayResults = append(alipayResults, DetailResult{
						Appid:        appid,
						Platform:     2, // 支付宝
						CouponAmount: result.CouponAmount,
						ActivityID:   result.ActivityID,
					})
				}
			}
			detailResults = append(detailResults, alipayResults...)
		}
	}

	// 按公司分组聚合数据
	// 记录每个公司按税地的核销金额
	companyTaxLocationAmounts := make(map[string]map[uint]float64)

	// 初始化每个公司的税地金额映射
	for _, appid := range customerAppIds {
		companyTaxLocationAmounts[appid] = make(map[uint]float64)
	}

	// 根据核销记录累计每个公司按税地的核销金额
	totalVerificationAmount := 0.0

	for _, item := range detailResults {
		appid := item.Appid
		if appid == "" {
			continue // 跳过没有找到对应appid的记录
		}

		// 累加总核销金额
		totalVerificationAmount += item.CouponAmount

		// 根据活动ID获取税地ID
		var taxLocationID uint = 0
		if item.Platform == 1 {
			// 微信核销记录
			taxLocationID = wechatStockTaxMap[item.ActivityID]
		} else if item.Platform == 2 {
			// 支付宝核销记录
			taxLocationID = alipayStockTaxMap[item.ActivityID]
		}

		// 按税地累计核销金额
		if taxLocationID > 0 {
			companyTaxLocationAmounts[appid][taxLocationID] += item.CouponAmount
		}
	}

	// 计算总佣金
	totalCommission := 0.0

	// 计算每个公司的佣金并汇总
	for appid, amounts := range companyTaxLocationAmounts {
		// 计算每个税地的佣金并汇总
		for taxLocationID, amount := range amounts {
			// 获取渠道给客户的税率
			customerRate := 0.0
			if rates, exists := customerRateMap[companyIds[appid]]; exists {
				if rate, hasRate := rates[taxLocationID]; hasRate {
					customerRate = rate
				}
			}

			// 获取平台给渠道的税率
			platformRate := 0.0
			if rateInfo, exists := platformRateMap[taxLocationID]; exists {
				platformRate = rateInfo["platform_rate"].(float64)
			}

			// 计算佣金差额
			if customerRate > platformRate {
				commission := amount * (customerRate - platformRate) / 100.0 // 转换为百分比
				totalCommission += commission
			}
		}
	}

	// 设置响应结果
	response.VerificationAmount = math.Round(totalVerificationAmount*100) / 100 // 保留两位小数
	response.CommissionAmount = math.Round(totalCommission*100) / 100           // 保留两位小数

	return response, nil
}

func (statisticsService *StatisticsService) WechatRedBank(searchStatisticsParams systemReq.SearchStatisticsParams, userId uint, authorityId uint) (res systemRes.SysStatisticsResponse, err error) {
	var categorizeResults systemRes.CategorizeResults
	// 查询公司appid和总余额
	// 客户权限设置
	var sysCompaniesApi system.SysCompaniesApi

	// 查询微信红包发送总数
	var wechatTotal int64
	// 查询微信红包发送成功总数
	var wechatReceiveTotal int64
	// 查询已核销总数
	var wechatVerifyTotal int64
	db1 := global.GVA_DB.Model(&system.SysWechatRedenvelopeListApi{})
	// 查询发送设置
	db2 := global.GVA_DB.Model(&system.SysWechatRedenvelopeSettingApi{})

	// 查询红包发送总金额
	var wechatAmountTotal float64
	// 查询成功领取红包总金额
	var wechatAmountReceiveTotal float64
	// 查询已核销红包总金额
	var wechatAmountVerifyTotal float64

	for _, date := range utils.Last7Days() {
		categorizeResults.CategoryDate = append(categorizeResults.CategoryDate, date.Format("2006-01-02"))
	}
	// 翻转日期
	categorizeResults.CategoryDate = utils.ReverseSlice(categorizeResults.CategoryDate)
	categorizeResults.SubjectNameList = []string{"制券金额", "领取金额", "核销金额"}
	categorizeResults.CategorizeName = "红包统计"
	// 存储客户的 AppIDs
	var customerAppIds []string
	// 客户权限设置
	if authorityId == 555 {
		// 查询用户的appid
		result := global.GVA_DB.Where("user_id = ? ", userId).First(&sysCompaniesApi)
		if result.Error != nil {
			res.Charset = categorizeResults
			// 记录不存在或查询错误
			return res, err
		}
		customerAppIds = []string{sysCompaniesApi.Appid}
		// 总金额
		res.Value1 = sysCompaniesApi.TotalAmount
	} else if authorityId == 666 { // 渠道用户，查看关联的客户数据
		// 获取渠道ID
		channelId := utils.GetChannelId(userId)
		if channelId == 0 {
			return res, errors.New("获取渠道信息失败")
		}

		// 获取渠道下所有客户的AppID
		err := global.GVA_DB.Raw(`
			SELECT c.appid 
			FROM channel_user_middle m 
			JOIN companies c ON m.companies_id = c.id 
			WHERE m.channel_user_id = ?
		`, userId).Pluck("appid", &customerAppIds).Error
		if err != nil {
			return res, fmt.Errorf("获取客户AppID失败: %v", err)
		}

		if len(customerAppIds) == 0 {
			return res, nil // 没有关联的客户，返回空结果
		}
	}

	if authorityId != 888 {
		if len(customerAppIds) == 0 {
			return res, nil // 直接返回空结果
		}
		db1.Where("appid IN ?", customerAppIds)
		db2.Where("appid IN ?", customerAppIds)
	}

	if searchStatisticsParams.AppId != "" {
		result := global.GVA_DB.Where("appid = ? ", searchStatisticsParams.AppId).First(&sysCompaniesApi)
		if result.Error != nil {
			res.Charset = categorizeResults
			// 记录不存在或查询错误
			return res, err
		}
		db1.Where("appid = ? ", sysCompaniesApi.Appid)
		db2.Where("appid = ? ", sysCompaniesApi.Appid)
		// 总金额
		res.Value1 = sysCompaniesApi.TotalAmount
	}

	// 如果有条件搜索 下方会自动创建搜索语句
	if searchStatisticsParams.StartCreatedAt != nil && searchStatisticsParams.EndCreatedAt != nil {
		// 查询所有红包总数
		db1.Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).Count(&wechatTotal)

		// 查询所有红包总金额（不过滤status，所有数据）
		db1.Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).
			Select("COALESCE(SUM(amount), 0)").
			Scan(&wechatAmountTotal)

		// 查询已下发数量和金额（状态>0，包括已下发、已核销、退回中、已退回）
		db1.Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).
			Where("status > ?", 0).
			Count(&wechatReceiveTotal)

		db1.Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).
			Where("status > ?", 0).
			Select("COALESCE(SUM(amount), 0)").
			Scan(&wechatAmountReceiveTotal)

		// 查询已核销数量和金额（状态为2）
		db1.Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).
			Where("status = ?", 2).
			Count(&wechatVerifyTotal)

		db1.Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).
			Where("status = ?", 2).
			Select("COALESCE(SUM(amount), 0)").
			Scan(&wechatAmountVerifyTotal)

		localTimeStartCreatedAt := searchStatisticsParams.StartCreatedAt.In(time.Local)
		localTimeEndCreatedAt := searchStatisticsParams.EndCreatedAt.In(time.Local)
		dateList, err := utils.GenerateDates(
			localTimeStartCreatedAt.Format("2006-01-02"),
			localTimeEndCreatedAt.Format("2006-01-02"),
		)
		if err != nil {
			return res, fmt.Errorf("日期生成失败: %v", err)
		}

		// 确保日期格式统一（YYYY-MM-DD）
		formattedDates := make([]string, len(dateList))
		for i, dateStr := range dateList {
			t, err := time.Parse("2006-1-2", dateStr) // 解析原始格式
			if err != nil {
				return res, fmt.Errorf("日期解析错误: %v", err)
			}
			formattedDates[i] = t.Format("2006-01-02") // 统一格式
		}

		categorizeResults.CategoryDate = formattedDates
	} else {
		// 查询所有红包总数
		db1.Count(&wechatTotal)

		// 查询所有红包总金额（不过滤status，所有数据）
		db1.Select("COALESCE(SUM(amount), 0)").Scan(&wechatAmountTotal)

		// 查询已下发数量和金额（状态>0）
		db1.Where("status > ?", 0).Count(&wechatReceiveTotal)
		db1.Where("status > ?", 0).Select("COALESCE(SUM(amount), 0)").Scan(&wechatAmountReceiveTotal)

		// 查询已核销数量和金额（状态为2）
		db1.Where("status = ?", 2).Count(&wechatVerifyTotal)
		db1.Where("status = ?", 2).Select("COALESCE(SUM(amount), 0)").Scan(&wechatAmountVerifyTotal)
	}

	// 总发送数量
	res.Value2 = wechatTotal
	// 总成功领取数量（状态>0）
	res.Value3 = wechatReceiveTotal
	// 核销数量（已核销，状态=2）
	res.Value4 = wechatVerifyTotal
	// 发送总金额 - 所有红包总金额（包括所有状态）
	res.Value5 = wechatAmountTotal
	// 领取总金额（状态>0）
	res.Value6 = wechatAmountReceiveTotal
	// 核销金额（已核销，状态=2）
	res.Value7 = wechatAmountVerifyTotal

	//制券金额（所有状态）- 不加任何状态条件，查询所有记录
	type CountListResults1 struct {
		FormattedDate string  `json:"formattedDate" gorm:"column:formattedDate"`
		Amount        float64 `json:"amount" gorm:"column:amount"`
	}
	var countListResults1 []CountListResults1

	// 查询日期区间内，所有记录的金额 (不过滤status)
	if searchStatisticsParams.StartCreatedAt != nil && searchStatisticsParams.EndCreatedAt != nil {
		err = db1.Select("DATE_FORMAT(created_at, '%Y-%m-%d') as formattedDate", "COALESCE(SUM(amount), 0) as amount").
			Where("created_at BETWEEN ? AND ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt).
			Group("formattedDate").
			Find(&countListResults1).Error
	} else {
		err = db1.Select("DATE_FORMAT(created_at, '%Y-%m-%d') as formattedDate", "COALESCE(SUM(amount), 0) as amount").
			Where("created_at >= ? AND created_at <= ?", categorizeResults.CategoryDate[0]+" 00:00:00", categorizeResults.CategoryDate[len(categorizeResults.CategoryDate)-1]+" 23:59:59").
			Group("formattedDate").
			Find(&countListResults1).Error
	}

	//领取金额（状态>0）
	type CountListResults2 struct {
		FormattedDate string  `json:"formattedDate" gorm:"column:formattedDate"`
		Amount        float64 `json:"amount" gorm:"column:amount"`
	}
	var countListResults2 []CountListResults2

	// 查询日期区间内，status > 0 的记录金额
	if searchStatisticsParams.StartCreatedAt != nil && searchStatisticsParams.EndCreatedAt != nil {
		err = db1.Select("DATE_FORMAT(created_at, '%Y-%m-%d') as formattedDate", "COALESCE(SUM(amount), 0) as amount").
			Where("created_at BETWEEN ? AND ? AND status > ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt, 0).
			Group("formattedDate").
			Find(&countListResults2).Error
	} else {
		err = db1.Select("DATE_FORMAT(created_at, '%Y-%m-%d') as formattedDate", "COALESCE(SUM(amount), 0) as amount").
			Where("created_at >= ? AND created_at <= ? AND status > ?", categorizeResults.CategoryDate[0]+" 00:00:00", categorizeResults.CategoryDate[len(categorizeResults.CategoryDate)-1]+" 23:59:59", 0).
			Group("formattedDate").
			Find(&countListResults2).Error
	}

	//核销金额（状态为2）
	type CountListResults3 struct {
		FormattedDate string  `json:"formattedDate" gorm:"column:formattedDate"`
		Amount        float64 `json:"amount" gorm:"column:amount"`
	}
	var countListResults3 []CountListResults3

	// 查询日期区间内，status = 2 的记录金额
	if searchStatisticsParams.StartCreatedAt != nil && searchStatisticsParams.EndCreatedAt != nil {
		err = db1.Select("DATE_FORMAT(created_at, '%Y-%m-%d') as formattedDate", "COALESCE(SUM(amount), 0) as amount").
			Where("created_at BETWEEN ? AND ? AND status = ?", searchStatisticsParams.StartCreatedAt, searchStatisticsParams.EndCreatedAt, 2).
			Group("formattedDate").
			Find(&countListResults3).Error
	} else {
		err = db1.Select("DATE_FORMAT(created_at, '%Y-%m-%d') as formattedDate", "COALESCE(SUM(amount), 0) as amount").
			Where("created_at >= ? AND created_at <= ? AND status = ?", categorizeResults.CategoryDate[0]+" 00:00:00", categorizeResults.CategoryDate[len(categorizeResults.CategoryDate)-1]+" 23:59:59", 2).
			Group("formattedDate").
			Find(&countListResults3).Error
	}

	// 定义统一数据结构
	type ChartSeries struct {
		Name string    `json:"name"`
		Type string    `json:"type"`
		Data []float64 `json:"data"`
	}

	// 创建日期索引映射
	dateIndex := make(map[string]int)
	for i, date := range categorizeResults.CategoryDate {
		dateIndex[date] = i
	}

	// 初始化结果集
	var dataRes []ChartSeries

	// 制券金额数据系列 - 所有记录，不过滤状态
	createSeries := ChartSeries{
		Name: "制券金额",
		Type: "line",
		Data: make([]float64, len(categorizeResults.CategoryDate)),
	}
	for _, item := range countListResults1 {
		if idx, ok := dateIndex[item.FormattedDate]; ok {
			createSeries.Data[idx] = item.Amount
		}
	}
	dataRes = append(dataRes, createSeries)

	// 领取金额数据系列 - status > 0
	receiveSeries := ChartSeries{
		Name: "领取金额",
		Type: "line",
		Data: make([]float64, len(categorizeResults.CategoryDate)),
	}
	for _, item := range countListResults2 {
		if idx, ok := dateIndex[item.FormattedDate]; ok {
			receiveSeries.Data[idx] = item.Amount
		}
	}
	dataRes = append(dataRes, receiveSeries)

	// 核销金额数据系列 - status = 2
	useSeries := ChartSeries{
		Name: "核销金额",
		Type: "line",
		Data: make([]float64, len(categorizeResults.CategoryDate)),
	}
	for _, item := range countListResults3 {
		if idx, ok := dateIndex[item.FormattedDate]; ok {
			useSeries.Data[idx] = item.Amount
		}
	}
	dataRes = append(dataRes, useSeries)

	// 赋值最终结果
	categorizeResults.CategoryCountList = dataRes

	res.Charset = categorizeResults

	return res, err
}
