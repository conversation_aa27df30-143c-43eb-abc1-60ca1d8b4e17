[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 13:19:46.013	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_writeoff_details.go:37	获取核销明细列表失败!	{"error": "Error 1267 (HY000): Illegal mix of collations (utf8mb4_general_ci,IMPLICIT) and (utf8mb4_unicode_ci,IMPLICIT) for operation '='"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 13:57:12.880	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:302	获取失败!	{"error": "sql: Scan error on column index 2, name \"activity_id\": converting driver.Value type []uint8 (\"ACT389ACV02913549\") to a int: invalid syntax; sql: Scan error on column index 2, name \"activity_id\": converting driver.Value type []uint8 (\"ACT389ACV02913549\") to a int: invalid syntax"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:12:54.387	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:20:26.363	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:26:45.475	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:32:33.009	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:35:13.775	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:36:32.258	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:37:03.654	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:39:32.306	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:41:40.223	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:44:42.561	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:46:54.814	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:52:09.180	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 15:48:15.587	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 15:53:17.267	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:320	添加失败!	{"error": "Error 3140 (22032): Invalid JSON text: \"The document is empty.\" at position 0 in value for column 'vouchar_test_issued_list.return_info'."}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 15:53:37.460	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:320	添加失败!	{"error": "Error 3140 (22032): Invalid JSON text: \"The document is empty.\" at position 0 in value for column 'vouchar_test_issued_list.return_info'."}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 15:55:42.127	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:01:39.693	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:07:37.726	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:09:36.919	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:13:38.028	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:20:22.364	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:29:35.313	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:32:10.123	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:34:40.771	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 20:56:32.972	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:14:53.145	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:20:57.745	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:26:27.550	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:31:08.223	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
