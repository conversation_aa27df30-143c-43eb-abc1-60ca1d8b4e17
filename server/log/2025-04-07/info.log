[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 13:18:59.572	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 13:18:59.586	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 13:18:59.943	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 13:18:59.943	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:13:33.113	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:13:33.128	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:13:33.473	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:13:33.473	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:20:48.950	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:20:48.964	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:20:49.288	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:20:49.289	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:27:03.716	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:27:03.731	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:27:04.051	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:27:04.051	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:32:59.090	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:32:59.105	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:32:59.461	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:32:59.461	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:35:34.730	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:35:34.746	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:35:35.048	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:35:35.048	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:36:53.714	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:36:53.729	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:36:54.050	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:36:54.050	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:37:29.447	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:37:29.463	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:37:29.792	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:37:29.792	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:39:49.466	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:39:49.480	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:39:49.827	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:39:49.828	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:42:04.056	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:42:04.070	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:42:04.537	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:42:04.537	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:45:03.820	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:45:03.835	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:45:04.191	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:45:04.192	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:47:13.054	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:47:13.070	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:47:13.433	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 14:47:13.433	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 15:01:30.050	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 15:01:30.067	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 15:01:30.424	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 15:01:30.425	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 15:48:26.488	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 15:48:26.503	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 15:48:26.841	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 15:48:26.842	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 15:56:07.036	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 15:56:07.052	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 15:56:07.403	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 15:56:07.403	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:02:06.865	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:02:06.878	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:02:07.215	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:02:07.218	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:08:08.828	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:08:08.843	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:08:09.229	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:08:09.230	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:10:00.796	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:10:00.811	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:10:01.141	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:10:01.142	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:14:08.787	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:14:08.803	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:14:09.141	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:14:09.142	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:20:48.655	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:20:48.670	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:20:48.981	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:20:48.982	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:30:07.068	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:30:07.083	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:30:07.413	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:30:07.414	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:32:39.559	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:32:39.572	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:32:40.068	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:32:40.068	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:35:07.083	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:35:07.111	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:35:07.632	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 17:35:07.633	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 20:56:56.692	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 20:56:56.704	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 20:56:57.011	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 20:56:57.011	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:16:00.540	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:16:00.554	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:16:00.863	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:16:00.864	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:21:25.692	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:21:25.708	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:21:25.973	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:21:25.973	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:27:21.308	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:27:21.339	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:27:21.846	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:27:21.846	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:31:39.566	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:31:39.577	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:31:40.177	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-07 21:31:40.178	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
