[github.com/flipped-aurora/gin-vue-admin/server]2025-04-08 08:36:40.617	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-08 10:18:17.172	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_user.go:58	登陆失败! 用户名不存在或者密码错误!	{"error": "密码错误"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-08 13:40:02.246	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-08 13:42:16.867	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_user.go:479	获取失败!	{"error": "record not found"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-08 13:42:29.846	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_user.go:58	登陆失败! 用户名不存在或者密码错误!	{"error": "record not found"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-08 13:42:39.812	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_user.go:58	登陆失败! 用户名不存在或者密码错误!	{"error": "record not found"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-08 14:04:36.418	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-08 15:00:26.175	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:6666: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-08 16:16:46.926	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-08 16:23:02.245	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-08 16:34:14.191	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-08 16:35:55.500	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:6666: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-08 20:20:49.917	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_menu.go:29	获取失败!	{"error": "invalid connection"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-08 20:25:10.475	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_user.go:479	获取失败!	{"error": "invalid connection"}
