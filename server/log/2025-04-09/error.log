[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 13:59:15.341	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:00:26.224	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:6666: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:01:05.463	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_user.go:58	登陆失败! 用户名不存在或者密码错误!	{"error": "密码错误"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:21:57.252	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:23:17.944	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:24:21.039	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:29:06.658	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:34:21.943	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:38:25.768	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:41:00.451	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:43:41.306	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:50:19.975	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:55:32.289	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:55	获取失败!	{"error": "Error 1064 (42000): You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'IN (NULL) AND `voucher_setting`.`deleted_at` IS NULL' at line 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 15:01:45.032	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 15:07:29.087	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 15:12:44.060	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 16:00:53.287	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:21:03.629	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_statistics.go:46	获取失败!	{"error": "sql: Scan error on column index 0, name \"SUM(coupon_amount)\": converting NULL to float64 is unsupported"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:24:18.640	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:25:04.720	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_statistics.go:46	获取失败!	{"error": "sql: Scan error on column index 0, name \"SUM(coupon_amount)\": converting NULL to float64 is unsupported"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:30:11.128	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:31:41.036	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:23:27.703	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_deducting_recharge_record.go:154	获取失败!	{"error": "Error 1064 (42000): You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'IN (?) AND status = ?  AND `companies`.`deleted_at` IS NULL ORDER BY id desc' at line 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:23:36.362	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_deducting_recharge_record.go:154	获取失败!	{"error": "Error 1064 (42000): You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'IN (?) AND status = ?  AND `companies`.`deleted_at` IS NULL ORDER BY id desc' at line 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:25:08.229	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:25:39.971	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_deducting_recharge_record.go:154	获取失败!	{"error": "Error 1064 (42000): You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'IN (?) AND status = ?  AND `companies`.`deleted_at` IS NULL ORDER BY id desc' at line 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:29:48.598	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:30:42.729	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
