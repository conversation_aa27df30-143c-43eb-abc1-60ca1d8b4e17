[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:00:01.263	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:00:01.278	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:00:01.650	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:00:01.650	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":6666"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:00:48.791	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:00:48.897	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:00:49.250	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:00:49.250	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:22:17.725	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:22:17.740	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:22:18.071	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:22:18.072	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:23:26.796	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:23:26.811	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:23:27.164	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:23:27.164	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:24:30.169	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:24:30.186	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:24:30.525	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:24:30.526	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:29:33.643	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:29:33.660	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:29:33.990	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:29:33.990	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:34:42.986	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:34:42.999	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:34:43.320	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:34:43.320	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:38:44.816	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:38:44.831	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:38:45.167	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:38:45.167	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:41:09.681	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:41:09.695	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:41:10.014	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:41:10.015	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:44:02.672	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:44:02.686	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:44:03.318	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:44:03.319	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:54:50.539	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:54:50.554	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:54:50.895	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 14:54:50.896	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 15:02:04.630	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 15:02:04.649	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 15:02:04.970	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 15:02:04.970	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 15:11:14.551	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 15:11:14.573	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 15:11:14.919	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 15:11:14.920	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 15:13:15.428	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 15:13:15.444	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 15:13:15.782	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 15:13:15.782	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:20:24.697	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:20:24.738	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:20:25.533	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:20:25.533	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:24:58.027	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:24:58.073	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:24:59.112	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:24:59.112	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:30:41.399	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:30:41.440	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:30:42.159	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 19:30:42.159	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:22:49.935	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:22:49.946	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:22:50.204	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:22:50.204	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:25:32.649	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:25:32.664	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:25:32.971	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:25:32.972	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:30:13.340	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:30:13.353	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:30:13.704	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:115	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-09 20:30:13.704	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
