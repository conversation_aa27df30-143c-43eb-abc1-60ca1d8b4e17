[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 16:38:58.567	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:06:47.072	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:08:44.748	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_statistics.go:46	获取失败!	{"error": "Error 1054 (42S22): Unknown column 'status' in 'where clause'"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:10:30.887	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:11:09.194	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_statistics.go:46	获取失败!	{"error": "Error 1054 (42S22): Unknown column 'status' in 'where clause'"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:12:47.196	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:13:29.228	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_statistics.go:46	获取失败!	{"error": "Error 1054 (42S22): Unknown column 'amount' in 'field list'"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:15:04.900	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:24:44.855	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:25:06.987	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:25:15.733	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:28:00.069	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:29:01.789	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:29:40.623	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:32:31.415	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:32:36.063	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_statistics.go:46	获取失败!	{"error": "sql: Scan error on column index 1, name \"amount\": converting driver.Value type float64 (\"0.009999999776482582\") to a int: invalid syntax; sql: Scan error on column index 1, name \"amount\": converting driver.Value type float64 (\"179.37999725341797\") to a int: invalid syntax; sql: Scan error on column index 1, name \"amount\": converting driver.Value type float64 (\"20.62000036239624\") to a int: invalid syntax; sql: Scan error on column index 1, name \"amount\": converting driver.Value type float64 (\"6.059999953955412\") to a int: invalid syntax; sql: Scan error on column index 1, name \"amount\": converting driver.Value type float64 (\"130.54999964125454\") to a int: invalid syntax; sql: Scan error on column index 1, name \"amount\": converting driver.Value type float64 (\"124.89999917708337\") to a int: invalid syntax"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:34:31.486	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:34:56.051	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_statistics.go:46	获取失败!	{"error": "sql: Scan error on column index 1, name \"amount\": converting driver.Value type float64 (\"0.009999999776482582\") to a int: invalid syntax; sql: Scan error on column index 1, name \"amount\": converting driver.Value type float64 (\"179.37999725341797\") to a int: invalid syntax; sql: Scan error on column index 1, name \"amount\": converting driver.Value type float64 (\"20.62000036239624\") to a int: invalid syntax; sql: Scan error on column index 1, name \"amount\": converting driver.Value type float64 (\"6.059999953955412\") to a int: invalid syntax; sql: Scan error on column index 1, name \"amount\": converting driver.Value type float64 (\"130.54999964125454\") to a int: invalid syntax; sql: Scan error on column index 1, name \"amount\": converting driver.Value type float64 (\"124.89999917708337\") to a int: invalid syntax"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:39:01.590	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:48:41.375	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:55:23.202	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:57:25.259	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:58:46.163	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 17:59:42.953	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_user.go:58	登陆失败! 用户名不存在或者密码错误!	{"error": "密码错误"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 18:00:06.534	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_user.go:58	登陆失败! 用户名不存在或者密码错误!	{"error": "密码错误"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 18:00:55.121	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-11 18:18:46.192	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
