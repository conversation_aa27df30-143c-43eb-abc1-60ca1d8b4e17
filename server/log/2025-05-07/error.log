[github.com/flipped-aurora/gin-vue-admin/server]2025-05-07 10:31:52.883	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	net.Listen error: listen tcp :9999: bind: address already in use
[github.com/flipped-aurora/gin-vue-admin/server]2025-05-07 10:33:40.676	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	net.Listen error: listen tcp :9999: bind: address already in use
[github.com/flipped-aurora/gin-vue-admin/server]2025-05-07 10:35:12.644	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-05-07 17:04:18.326	[31mer<PERSON>r[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_deducting_recharge_record.go:154	获取失败!	{"error": "invalid connection"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-05-07 20:02:20.866	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "invalid connection"}
