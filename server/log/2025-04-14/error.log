[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 15:09:00.177	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:67	register table failed	{"error": "Error 1366 (HY000): Incorrect integer value: 'SUCCESS' for column 'send_status' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 15:09:43.642	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:67	register table failed	{"error": "Error 1366 (HY000): Incorrect integer value: 'SUCCESS' for column 'send_status' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 15:10:25.215	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:67	register table failed	{"error": "Error 1366 (HY000): Incorrect integer value: 'SUCCESS' for column 'send_status' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 15:15:19.426	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:67	register table failed	{"error": "Error 1366 (HY000): Incorrect integer value: 'SUCCESS' for column 'send_status' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 15:15:30.692	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:67	register table failed	{"error": "Error 1366 (HY000): Incorrect integer value: 'SUCCESS' for column 'send_status' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 15:19:19.851	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:364	查询失败!	{"error": "record not found"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 15:20:21.232	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:364	查询失败!	{"error": "record not found"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 15:20:53.287	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:364	查询失败!	{"error": "record not found"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 15:21:51.093	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:364	查询失败!	{"error": "record not found"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 15:47:02.502	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 16:13:37.664	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 16:14:47.926	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 16:20:03.390	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 16:20:11.245	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 16:28:21.024	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_statistics.go:46	获取失败!	{"error": "Error 1054 (42S22): Unknown column 'appid' in 'where clause'"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 16:28:24.136	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_statistics.go:46	获取失败!	{"error": "Error 1054 (42S22): Unknown column 'appid' in 'where clause'"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 16:30:42.551	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 16:36:05.351	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 17:05:22.809	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:6666: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 17:06:08.898	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_user.go:58	登陆失败! 用户名不存在或者密码错误!	{"error": "密码错误"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-14 17:08:34.413	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
