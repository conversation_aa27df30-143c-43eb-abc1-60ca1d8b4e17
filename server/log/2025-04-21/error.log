[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 11:05:27.734	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:33:50.053	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:34:43.453	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:321	添加失败!	{"error": "crypto/des: invalid key size 32"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:40:06.232	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:43:29.798	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:47:48.059	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:53:10.358	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:59:22.843	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:04:28.452	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:17:13.760	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:18:07.187	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:20:06.331	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:20:57.161	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:598	---------第三方接口返回错误状态码:	{"statusCode": 500, "response": "{\"message\":\"Method App\\\\Http\\\\Controllers\\\\Sdk\\\\WechatpayController::alipay_distribute_voucher_test_api does not exist.\",\"status_code\":500}"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:20:57.161	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:321	添加失败!	{"error": "下发失败！HTTP状态码: 500"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:21:51.715	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:598	---------第三方接口返回错误状态码:	{"statusCode": 500, "response": "{\"message\":\"Method App\\\\Http\\\\Controllers\\\\Sdk\\\\WechatpayController::alipay_distribute_voucher_test_api does not exist.\",\"status_code\":500}"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:21:51.717	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:321	添加失败!	{"error": "下发失败！HTTP状态码: 500"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:21:57.957	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:598	---------第三方接口返回错误状态码:	{"statusCode": 500, "response": "{\"message\":\"Method App\\\\Http\\\\Controllers\\\\Sdk\\\\WechatpayController::alipay_distribute_voucher_test_api does not exist.\",\"status_code\":500}"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:21:57.957	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:321	添加失败!	{"error": "下发失败！HTTP状态码: 500"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:23:21.886	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:598	---------第三方接口返回错误状态码:	{"statusCode": 500, "response": "{\"message\":\"Method App\\\\Http\\\\Controllers\\\\Sdk\\\\WechatpayController::alipay_distribute_voucher_test_api does not exist.\",\"status_code\":500}"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:23:21.887	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:321	添加失败!	{"error": "下发失败！HTTP状态码: 500"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:24:24.208	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:598	---------第三方接口返回错误状态码:	{"statusCode": 500, "response": "\n{\"message\":\"Undefined variable: platform_certificate_serial_or_public_key_id\",\"status_code\":500}"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:24:24.209	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:321	添加失败!	{"error": "下发失败！HTTP状态码: 500"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:29:28.480	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:30:13.245	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:607	接口返回错误	{"msg": "税地信息配置错误"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:30:13.246	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:321	添加失败!	{"error": "税地信息配置错误"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:30:46.670	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:618	---------第三方接口返回错误状态码:	{"statusCode": 500, "response": "\n{\"message\":\"Undefined variable: alipayConfig\",\"status_code\":500}"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:30:46.671	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:321	添加失败!	{"error": "下发失败！HTTP状态码: 500"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:33:13.729	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:618	---------第三方接口返回错误状态码:	{"statusCode": 500, "response": "\n{\"message\":\"Call to a member function setServerUrl() on null\",\"status_code\":500}"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:33:13.729	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:321	添加失败!	{"error": "下发失败！HTTP状态码: 500"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:33:24.201	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:618	---------第三方接口返回错误状态码:	{"statusCode": 500, "response": "\n{\"message\":\"Call to a member function setServerUrl() on null\",\"status_code\":500}"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:33:24.201	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:321	添加失败!	{"error": "下发失败！HTTP状态码: 500"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:33:58.161	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:618	---------第三方接口返回错误状态码:	{"statusCode": 500, "response": "\n{\"message\":\"Undefined variable: denomination_info\",\"status_code\":500}"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:33:58.161	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:321	添加失败!	{"error": "下发失败！HTTP状态码: 500"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:35:21.155	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:618	---------第三方接口返回错误状态码:	{"statusCode": 500, "response": "\n{\"message\":\"Undefined index: activity_id\",\"status_code\":500}"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:35:21.156	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:321	添加失败!	{"error": "下发失败！HTTP状态码: 500"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:35:51.489	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:41:01.079	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:43:14.761	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:45:16.175	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:49:42.436	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:50:31.380	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:640	业务处理返回错误	{"msg": "Business Failed"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:50:31.381	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:321	添加失败!	{"error": "Business Failed"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:53:26.826	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:54:22.375	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:640	业务处理返回错误	{"msg": "Business Failed"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:54:22.376	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:321	添加失败!	{"error": "Business Failed"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:55:03.811	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:55:45.165	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:641	业务处理返回错误	{"msg": "Business Failed"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:55:45.165	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:321	添加失败!	{"error": "Business Failed"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:01:29.765	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:03:13.511	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:641	业务处理返回错误	{"msg": "Success"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:03:13.512	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:321	添加失败!	{"error": "Success"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:04:01.103	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:04:57.753	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:18:16.882	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:20:42.077	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:811	---------第三方接口返回错误状态码:	{"statusCode": 500, "response": "\n{\"message\":\"Undefined index: account_type\",\"status_code\":500}"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:20:42.078	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:339	添加失败!	{"error": "下发失败！HTTP状态码: 500"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:21:33.367	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:22:29.944	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:811	---------第三方接口返回错误状态码:	{"statusCode": 500, "response": "{\"message\":\"Undefined index: custom_identification\",\"status_code\":500}"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:22:29.944	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:339	添加失败!	{"error": "下发失败！HTTP状态码: 500"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:23:06.341	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:765	接口返回错误	{"msg": "activity_id-参数错误"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:23:06.343	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:339	添加失败!	{"error": "activity_id-参数错误"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:24:57.580	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:765	接口返回错误	{"msg": "activity_id-参数错误"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:24:57.581	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:339	添加失败!	{"error": "activity_id-参数错误"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:25:54.187	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:811	---------第三方接口返回错误状态码:	{"statusCode": 500, "response": "{\"message\":\"Client error: `POST https:\\/\\/api.mch.weixin.qq.com\\/v3\\/marketing\\/favor\\/users\\/2\\/coupons` resulted in a `400 Bad Request` response:\\n{\\\"code\\\":\\\"INVALID_REQUEST\\\",\\\"message\\\":\\\"appid\\u4e0eopenid\\u4e0d\\u5339\\u914d\\\"}\\n\",\"code\":400,\"status_code\":500}"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:25:54.188	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:339	添加失败!	{"error": "下发失败！HTTP状态码: 500"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:27:53.563	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:30:03.378	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:810	---------第三方接口返回错误状态码:	{"statusCode": 500, "response": "{\"message\":\"Client error: `POST https:\\/\\/api.mch.weixin.qq.com\\/v3\\/marketing\\/favor\\/users\\/2\\/coupons` resulted in a `400 Bad Request` response:\\n{\\\"code\\\":\\\"INVALID_REQUEST\\\",\\\"message\\\":\\\"appid\\u4e0eopenid\\u4e0d\\u5339\\u914d\\\"}\\n\",\"code\":400,\"status_code\":500}"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:30:03.379	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_alipay_coupon.go:339	添加失败!	{"error": "下发失败！HTTP状态码: 500"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:39:01.872	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:41:11.036	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:41:53.700	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
