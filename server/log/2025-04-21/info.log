[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 11:03:39.728	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 11:03:39.747	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 11:03:40.071	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 11:03:40.071	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 11:05:39.948	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 11:05:39.961	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 11:05:40.262	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 11:05:40.263	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:34:24.389	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:34:24.452	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:34:25.246	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:34:25.247	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:40:43.292	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:40:43.378	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:40:44.307	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:40:44.308	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:44:02.415	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:44:02.479	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:44:03.312	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:44:03.312	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:48:25.440	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:48:25.504	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:48:26.327	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:48:26.328	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:51:10.357	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:600	---------第三方接口返回原始数据:{"code":400,"msg":"\u7b7e\u540d\u9519\u8bef"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:53:40.802	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:53:40.863	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:53:41.689	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:53:41.689	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 15:54:00.546	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:600	---------第三方接口返回原始数据:{"code":400,"msg":"\u7b7e\u540d\u9519\u8bef"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:00:06.940	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:00:07.006	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:00:07.850	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:00:07.850	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:00:30.682	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:600	---------第三方接口返回原始数据:{"code":400,"msg":"\u7b7e\u540d\u9519\u8bef"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:05:01.666	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:05:01.728	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:05:03.732	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:05:03.732	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:05:18.725	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:595	---------第三方接口返回原始数据:{"code":400,"msg":"\u7b7e\u540d\u9519\u8bef"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:17:31.491	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:17:31.552	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:17:32.375	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:17:32.376	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:17:42.200	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:595	---------第三方接口返回原始数据:{"code":400,"msg":"\u7b7e\u540d\u9519\u8bef"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:18:33.468	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:18:33.530	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:18:34.345	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:18:34.345	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:18:42.876	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:588	---------第三方接口返回原始数据:{"code":400,"msg":"\u7b7e\u540d\u9519\u8bef"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:20:36.032	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:20:36.093	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:20:36.918	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:20:36.919	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:20:57.160	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:588	---------第三方接口返回原始数据:{"message":"Method App\\Http\\Controllers\\Sdk\\WechatpayController::alipay_distribute_voucher_test_api does not exist.","status_code":500}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:21:51.713	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:588	---------第三方接口返回原始数据:{"message":"Method App\\Http\\Controllers\\Sdk\\WechatpayController::alipay_distribute_voucher_test_api does not exist.","status_code":500}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:21:57.957	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:588	---------第三方接口返回原始数据:{"message":"Method App\\Http\\Controllers\\Sdk\\WechatpayController::alipay_distribute_voucher_test_api does not exist.","status_code":500}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:23:21.884	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:588	---------第三方接口返回原始数据:{"message":"Method App\\Http\\Controllers\\Sdk\\WechatpayController::alipay_distribute_voucher_test_api does not exist.","status_code":500}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:24:24.207	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:588	---------第三方接口返回原始数据:
{"message":"Undefined variable: platform_certificate_serial_or_public_key_id","status_code":500}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:25:19.168	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:588	---------第三方接口返回原始数据:
{"code":-1,"msg":"\u7a0e\u5730\u4fe1\u606f\u914d\u7f6e\u9519\u8bef"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:30:00.913	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:30:00.975	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:30:01.779	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:30:01.780	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:30:13.245	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:588	---------第三方接口返回原始数据:
{"code":-1,"msg":"\u7a0e\u5730\u4fe1\u606f\u914d\u7f6e\u9519\u8bef"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:30:46.668	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:588	---------第三方接口返回原始数据:
{"message":"Undefined variable: alipayConfig","status_code":500}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:33:13.727	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:588	---------第三方接口返回原始数据:
{"message":"Call to a member function setServerUrl() on null","status_code":500}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:33:24.201	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:588	---------第三方接口返回原始数据:
{"message":"Call to a member function setServerUrl() on null","status_code":500}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:33:58.159	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:588	---------第三方接口返回原始数据:
{"message":"Undefined variable: denomination_info","status_code":500}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:35:21.153	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:588	---------第三方接口返回原始数据:
{"message":"Undefined index: activity_id","status_code":500}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:36:18.655	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:36:18.717	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:36:19.531	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:36:19.532	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:36:53.333	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:589	---------第三方接口返回原始数据:
{"code":0,"msg":"\u8c03\u7528\u6210\u529f","data":{"msg":"Business Failed","code":"40004","sub_msg":"\u6d3b\u52a8\u4e0d\u5b58\u5728","sub_code":"ACTIVITY_NOT_EXIST","send_status":"FAILED"}}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:41:31.235	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:41:31.306	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:41:32.130	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:41:32.131	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:41:52.611	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:589	---------第三方接口返回原始数据:
{"code":0,"msg":"\u8c03\u7528\u6210\u529f","data":{"msg":"Business Failed","code":"40004","sub_msg":"\u6d3b\u52a8\u4e0d\u5b58\u5728","sub_code":"ACTIVITY_NOT_EXIST","send_status":"FAILED"}}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:43:46.723	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:43:46.790	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:43:47.651	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:43:47.652	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:44:16.713	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:589	---------第三方接口返回原始数据:
{"code":0,"msg":"\u8c03\u7528\u6210\u529f","data":{"msg":"Business Failed","code":"40004","sub_msg":"\u6d3b\u52a8\u4e0d\u5b58\u5728","sub_code":"ACTIVITY_NOT_EXIST","send_status":"FAILED"}}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:45:49.180	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:45:49.265	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:45:50.069	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:45:50.070	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:45:59.949	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:589	---------第三方接口返回原始数据:
{"code":0,"msg":"\u8c03\u7528\u6210\u529f","data":{"msg":"Business Failed","code":"40004","sub_msg":"\u6d3b\u52a8\u4e0d\u5b58\u5728","sub_code":"ACTIVITY_NOT_EXIST","send_status":"FAILED"}}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:50:24.674	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:50:24.737	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:50:25.954	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:50:25.955	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:50:31.380	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:589	---------第三方接口返回原始数据:
{"code":0,"msg":"\u8c03\u7528\u6210\u529f","data":{"msg":"Business Failed","code":"40004","sub_msg":"\u6d3b\u52a8\u4e0d\u5b58\u5728","sub_code":"ACTIVITY_NOT_EXIST","send_status":"FAILED"}}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:53:46.565	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:53:46.629	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:53:48.152	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:53:48.152	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:54:22.374	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:589	---------第三方接口返回原始数据:
{"code":0,"msg":"\u8c03\u7528\u6210\u529f","data":{"msg":"Business Failed","code":"40004","sub_msg":"\u6d3b\u52a8\u4e0d\u5b58\u5728","sub_code":"ACTIVITY_NOT_EXIST","send_status":"FAILED"}}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:55:37.195	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:55:37.264	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:55:38.129	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:55:38.130	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 16:55:45.165	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:589	---------第三方接口返回原始数据:
{"code":0,"msg":"\u8c03\u7528\u6210\u529f","data":{"msg":"Business Failed","code":"40004","sub_msg":"\u6d3b\u52a8\u4e0d\u5b58\u5728","sub_code":"ACTIVITY_NOT_EXIST","send_status":"FAILED"}}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:02:50.434	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:02:50.495	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:02:51.524	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:02:51.524	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:03:13.510	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:589	---------第三方接口返回原始数据:
{"code":0,"msg":"\u8c03\u7528\u6210\u529f","data":{"code":"10000","msg":"Success","account_no":"ern***@163.com","activity_id":"ACT389ACV02913549","activity_order_id":"20250421170313ACT05555ACV00560097935","discount_threshold_amt":0,"discount_type":"reduce","discount_value":200,"send_amount":200,"send_status":"SUCCESS","voucher_id":"202504210007300255660USP7XSJ"}}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:04:41.278	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:04:41.343	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:04:42.178	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:04:42.179	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:05:26.142	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:05:26.210	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:05:27.018	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:05:27.018	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:05:47.347	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:589	---------第三方接口返回原始数据:
{"code":0,"msg":"\u8c03\u7528\u6210\u529f","data":{"code":"10000","msg":"Success","account_no":"ern***@163.com","activity_id":"ACT389ACV02913549","activity_order_id":"20250421170547ACT05555ACV00559997216","discount_threshold_amt":0,"discount_type":"reduce","discount_value":200,"send_amount":200,"send_status":"SUCCESS","voucher_id":"202504210007300255660USC0TAH"}}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:19:53.267	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:19:53.336	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:19:54.248	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:19:54.249	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:20:42.076	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:746	---------第三方接口返回原始数据:
{"message":"Undefined index: account_type","status_code":500}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:22:01.179	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:22:01.241	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:22:02.041	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:22:02.042	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:22:29.942	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:746	---------第三方接口返回原始数据:{"message":"Undefined index: custom_identification","status_code":500}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:23:06.340	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:746	---------第三方接口返回原始数据:{"code":-1,"msg":"activity_id-\u53c2\u6570\u9519\u8bef"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:24:57.578	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:746	---------第三方接口返回原始数据:{"code":-1,"msg":"activity_id-\u53c2\u6570\u9519\u8bef"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:25:54.186	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:746	---------第三方接口返回原始数据:{"message":"Client error: `POST https:\/\/api.mch.weixin.qq.com\/v3\/marketing\/favor\/users\/2\/coupons` resulted in a `400 Bad Request` response:\n{\"code\":\"INVALID_REQUEST\",\"message\":\"appid\u4e0eopenid\u4e0d\u5339\u914d\"}\n","code":400,"status_code":500}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:28:55.768	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:28:55.834	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:28:56.826	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:28:56.826	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:30:03.376	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:745	---------第三方接口返回原始数据:{"message":"Client error: `POST https:\/\/api.mch.weixin.qq.com\/v3\/marketing\/favor\/users\/2\/coupons` resulted in a `400 Bad Request` response:\n{\"code\":\"INVALID_REQUEST\",\"message\":\"appid\u4e0eopenid\u4e0d\u5339\u914d\"}\n","code":400,"status_code":500}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:31:21.048	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:745	---------第三方接口返回原始数据:{"code":0,"msg":"\u8c03\u7528\u6210\u529f","data":[]}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:35:09.208	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_alipay_coupon.go:745	---------第三方接口返回原始数据:{"code":0,"msg":"\u8c03\u7528\u6210\u529f","data":{"coupon_id":"100551420034"}}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:40:53.525	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:40:53.590	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:40:54.414	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:40:54.415	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:41:48.176	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:41:48.241	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:41:49.187	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-21 17:41:49.187	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
