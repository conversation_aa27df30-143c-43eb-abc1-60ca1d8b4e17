[github.com/flipped-aurora/gin-vue-admin/server]2025-04-17 10:36:31.170	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-17 10:56:08.949	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-17 11:03:38.539	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-17 11:05:55.723	[31mer<PERSON>r[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-17 11:10:05.996	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-17 11:20:54.259	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:6666: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-17 11:21:31.141	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-17 11:21:36.439	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-17 11:21:37.911	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-17 11:21:41.439	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-17 11:21:43.794	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-17 11:21:46.445	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-17 11:24:12.340	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/middleware/operation.go:120	create operation record error:	{"error": "Error 1406 (22001): Data too long for column 'resp' at row 1"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-17 11:32:24.316	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_user.go:58	登陆失败! 用户名不存在或者密码错误!	{"error": "record not found"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-17 11:32:42.179	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_user.go:58	登陆失败! 用户名不存在或者密码错误!	{"error": "密码错误"}
