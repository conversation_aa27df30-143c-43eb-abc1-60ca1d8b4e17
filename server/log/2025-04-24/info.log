[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 16:49:45.725	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 16:49:45.737	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 16:49:46.026	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 16:49:46.026	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 16:54:17.134	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 16:54:17.148	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 16:54:17.440	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 16:54:17.440	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 16:57:50.583	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 16:57:50.598	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 16:57:50.900	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 16:57:50.901	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:01:28.058	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:01:28.073	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:01:28.389	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:01:28.390	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:17:05.899	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:17:05.912	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:17:06.210	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:17:06.211	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:24:40.262	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:24:40.275	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:24:40.579	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:24:40.580	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:42:31.066	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:42:31.079	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:42:31.365	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:42:31.365	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:59:57.074	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:59:57.086	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:59:57.373	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:59:57.374	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:12.790	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_menu.go:51	GetBaseMenuTree - 获取用户权限菜单树	{"authorityID": 555}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:12.790	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_menu.go:281	GetBaseMenuTree - 获取基础菜单树	{"authorityID": 555}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:12.791	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_menu.go:205	getBaseMenuTreeMap - 获取菜单树映射	{"authorityID": 555}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:12.881	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_menu.go:270	树结构中的父节点IDs	{"parentIds": [0, 24, 51, 58, 15, 38, 56, 32, 54, 61, 64, 3, 43, 11]}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:12.881	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_menu.go:307	根菜单数量	{"count": 18}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:12.881	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_menu.go:320	最终菜单树	{"count": 18}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:12.881	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_menu.go:61	获取到菜单树	{"菜单数量": 18}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:18.630	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_menu.go:51	GetBaseMenuTree - 获取用户权限菜单树	{"authorityID": 555}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:18.630	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_menu.go:281	GetBaseMenuTree - 获取基础菜单树	{"authorityID": 555}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:18.630	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_menu.go:205	getBaseMenuTreeMap - 获取菜单树映射	{"authorityID": 555}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:18.727	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_menu.go:270	树结构中的父节点IDs	{"parentIds": [24, 43, 61, 15, 38, 51, 58, 3, 54, 56, 64, 11, 0, 32]}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:18.728	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_menu.go:307	根菜单数量	{"count": 18}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:18.728	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_menu.go:320	最终菜单树	{"count": 18}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:18.728	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_menu.go:61	获取到菜单树	{"菜单数量": 18}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:22.416	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_menu.go:51	GetBaseMenuTree - 获取用户权限菜单树	{"authorityID": 555}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:22.416	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_menu.go:281	GetBaseMenuTree - 获取基础菜单树	{"authorityID": 555}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:22.417	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_menu.go:205	getBaseMenuTreeMap - 获取菜单树映射	{"authorityID": 555}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:22.513	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_menu.go:270	树结构中的父节点IDs	{"parentIds": [54, 15, 56, 61, 64, 0, 43, 51, 58, 3, 11, 24, 32, 38]}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:22.514	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_menu.go:307	根菜单数量	{"count": 18}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:22.514	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/service/system/sys_menu.go:320	最终菜单树	{"count": 18}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:00:22.514	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_menu.go:61	获取到菜单树	{"菜单数量": 18}
