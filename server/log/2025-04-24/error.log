[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 16:18:26.965	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_user.go:58	登陆失败! 用户名不存在或者密码错误!	{"error": "密码错误"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 16:49:18.041	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 16:53:55.742	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 16:57:27.867	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:01:05.638	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:16:41.063	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:23:18.021	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:38:48.110	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 17:59:22.215	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:40	accept tcp [::]:9999: use of closed network connection
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:51:22.018	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_menu.go:31	获取失败!	{"error": "dial tcp ***************:3306: connect: network is unreachable"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:51:22.106	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_user.go:481	获取失败!	{"error": "dial tcp ***************:3306: connect: network is unreachable"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-24 18:57:27.893	[31merror[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/api/v1/system/sys_menu.go:31	获取失败!	{"error": "dial tcp ***************:3306: connect: network is unreachable"}
