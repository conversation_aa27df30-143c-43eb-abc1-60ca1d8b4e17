[github.com/flipped-aurora/gin-vue-admin/server]2025-04-22 10:46:14.206	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-22 10:46:14.220	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-22 10:46:14.513	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-22 10:46:14.513	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":6666"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-22 13:50:31.612	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-22 13:50:31.627	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-22 13:50:31.942	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-22 13:50:31.943	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":6666"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-22 13:51:09.490	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-22 13:51:09.502	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-22 13:51:09.766	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-22 13:51:09.766	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-22 13:55:50.835	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/gorm.go:76	register table success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-22 13:55:50.848	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:60	register swagger handler
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-22 13:55:51.133	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/initialize/router.go:117	router register success
[github.com/flipped-aurora/gin-vue-admin/server]2025-04-22 13:55:51.133	[34minfo[0m	/Users/<USER>/Documents/SynologyDrive/qimatong/server/core/server.go:38	server run success on 	{"address": ":9999"}
